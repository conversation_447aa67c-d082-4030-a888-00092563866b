<div class="row">
  <div class="col-sm-12">
    <div class="m-card">
      <div class="card-head">
        <span>拒收列表</span>
      </div>
      <div class="card-body">

        <div class="search-form-wrap">
          <form class="form form-horizontal search-form well" form-Validator="rejectApplyCtrl" name="formValidator">
            <div class="form-group form-group-sm">
              <!-- <yx-betweenday label="更新时间" label-size="2" start-time="search.updateStartTime"
                end-time="search.updateEndTime" class="pull-left"> </yx-betweenday> -->
                <label class="col-sm-2 control-label">
                  更新时间：
              </label>
              <div class="form-group" style="float:left;">
                <div class="pull-left">
                    <div class="input-group date">
                        <input name="datetimepicker1" ng-refenddate="datetimepicker2" datetimepicker
                            ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}"
                            ng-model="search.updateStartTime" type='text' class="form-control"
                            datetimecheck return-type="milliseconds" required />
                        <span class="input-group-addon"><span class="icon icon-calendar"></span>
                        </span>
                    </div>
                </div>
                <p class="text-center pull-left" style="line-height: 34px; margin: 0 10px;">至</p>
                <div class=" pull-left">
                    <div class="input-group date">
                        <input name="datetimepicker2" ng-refbegindate="datetimepicker1" datetimepicker
                            ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}"
                            ng-model="search.updateEndTime" type='text' class="form-control"
                            datetimecheck return-type="milliseconds" required />
                        <span class="input-group-addon"><span class="icon icon-calendar"></span>
                        </span>
                    </div>
                </div>
              </div>
              <label class=" col-sm-1 control-label">
                用户身份：
              </label>
              <div >
                <select ng-model="search.userCrmType" class=" form-control" style="width: 10%;height: 37px;" >
                  <option value="-1">全部</option>
                  <option value="1">超级VIP</option>
                </select>
              </div>         
            </div>
           
            <div class="form-group form-group-sm">
              <label class="col-sm-2 control-label">
                订单号：
              </label>
              <div class="col-sm-3">
                <input type="text" class="form-control" name="orderId" ng-model="search.orderId" postiveint>
              </div>
              <div ng-init="search.mode = 1">
                <select ng-model="search.mode" class="col-sm-1 form-control" style="margin-left: 60px;width: 100px">
                  <option value="1">商品ID</option>
                  <option value="2">SKUID</option>
                </select>
              </div>
              <div class="col-sm-3" style="margin-left: 10px">
                <input type="text" class="form-control" name="otherId" ng-model="search.otherId" postiveint>
              </div>
            </div>

            <div class="form-group form-group-sm">
              <label class="col-sm-2 control-label"> 拒收申请单号：</label>
              <div class="col-sm-3">
                <input type="text" class="form-control" name="orderId" ng-model="search.applyId" postiveint>
              </div>
              <label class="col-sm-2 control-label" style="margin-left: 60px;width: 100px"> 物流单号:</label>
              <div class="col-sm-3" style="margin-left: 10px">
                <input type="text" class="form-control" name="orderId" ng-model="search.trackingNum" postiveint>
              </div>
            </div>

            <div class="form-group form-group-sm">
              <label class="col-sm-2 control-label">按状态：</label>
              <div class="col-sm-3">
                <select class="form-control" ng-model="search.status">
                  <option value="-1">全部</option>
                  <option value="1">待审核</option>
                  <option value="3">待仓库收货</option>
                  <option value="4">退款完成</option>
                  <option value="5">审核不通过</option>
                </select>
              </div>
              <label class="col-sm-2 control-label" style="margin-left: 60px;width: 100px">拒收场景：</label>
              <div class="col-sm-3" style="margin-left: 10px">
                <select class="form-control" ng-model="search.scene">
                  <option value="-1">全部</option>
                  <option value="2">拒收拦截</option>
                  <option value="3">系统拦截</option>
                </select>
              </div>
            </div>
            <div class="form-group form-group-sm">
              <label class="col-sm-2 control-label">发货仓：</label>
                <div class="col-sm-3">
                  <select class="form-control" ng-model="search.deliveryStoreHouseType">
                    <option value="-1">全部</option>
                    <option value="1">内仓</option>
                    <option value="2">外仓</option>
                  </select>
                </div>
                <label class="col-sm-2 control-label" style="margin-left: 60px;width: 100px">
                  拦截方式：
                </label>
                <div class="col-sm-3" style="margin-left: 10px">
                    <select class="form-control" ng-model="search.intercept"
                        ng-options="status[0] as status[1] for status in INTERCEPT_STATUSES">
                    </select>
                </div>
            </div>
            <div class="col-sm-offset-2">
              <button type="submit" class="btn btn-primary" form-Submit="query()">查询</button>
            </div>
          </form>
        </div>

        <yx-table border="false" pg="true"
          td-group="['更新时间', '用户名','订单号','包裹号','拒收申请单号','联系人','电话','退货商品','正向物流单号','逆向物流单号','退货原因','状态','拦截方式', '操作']">
          <yx-tr ng-repeat="item in result">
            <yx-td>{{item.updateTime | date:'yyyy-MM-dd HH:mm:ss'}}</yx-td>
            <yx-td>
              <span>{{item.userName}}</span>
              <div>
                <span ng-repeat="label in item.userLabels">
                  <span ng-if="label.tip" tooltip="{{label.tip}}" ng-class="{'credit-link': isCreditLink(label.desc)}"
                    ng-click="openLink(label.desc, item.userName, item.userId)" tooltip-placement="top" class="label"
                    ng-style="{'background': label.color}">{{label.desc}}</span>
                  <span ng-if="!label.tip" class="label" ng-class="{'credit-link': isCreditLink(label.desc)}"
                    ng-click="openLink(label.desc, item.userName, item.userId)"
                    ng-style="{'background': label.color}">{{label.desc}}</span>
                </span>
              </div>
            </yx-td>
            <yx-td><a href="#/search/orderDetail/{{item.orderId}}" target="_blank">{{item.orderId}}</a></yx-td>
            <yx-td>{{item.packageId}}</yx-td>
            <yx-td>
              {{item.applyId}}
              <div class="label f-block f-m-t-5" ng-repeat="tag in item.userTags" ng-style="{'background': tag.color}">
                {{tag.desc}}</div>
            </yx-td>
            <yx-td>{{item.contactName}}</yx-td>
            <yx-td>
              <span copy-phone phone="{{item.contactMobile}}">{{item.contactMobile}}</span>
            </yx-td>
            <yx-td class="max-width-200">
              <div ng-repeat="sku in item.skuList">
                <span>{{sku.itemNickName || sku.itemName}}*{{sku.count}}</span>
                <div ng-repeat="tag in sku.customTagList">
                  <span ng-style="{'backgroundColor': tag.bgColor, 'color': tag.textColor}" class="label">{{tag.tagName}}</span>
                </div>
              </div>
            </yx-td>
            <!-- <yx-td>
              {{item.amount | price}}
              <div ng-if="item.refundPointSum">{{item.refundPointSum}}积分</div>
            </yx-td> -->
            <!-- TODO -->
            <yx-td>
              <div ng-repeat="express in item.forwardExpressInfos">
                <a href="javascript:;" ng-click="showReturnDelivery(express.forwardCompany, express.forwardTrackingNum)">
                  <span ng-if="express.forwardTrackingNum">{{express.forwardCompany}}*{{express.forwardTrackingNum}}</span>
                </a>
              </div>
            </yx-td>
            <!-- <yx-td>
              <div ng-repeat="item in item.rejectExpress">
                <a href="javascript:;" ng-click="showReturnDelivery(item.trackingCompany,item.trackingNum, item.id)">
                  <span>{{item.trackingCompany}}*{{item.trackingNum}}</span>
                </a>
              </div>
            </yx-td> -->
            <yx-td>
              <div ng-repeat="item in item.rejectExpress">
                <a href="javascript:;" ng-click="showReturnDelivery(item.trackingCompany,item.trackingNum, item.id)">
                  <span ng-if="item.trackingNum">{{item.trackingCompany}}*{{item.trackingNum}}</span>
                </a>
              </div>
            </yx-td>
            <yx-td>{{item.packageIntercept | rejectReasonFilter}}</yx-td>
            <yx-td>{{item.status | rejectApplyFilter}}</yx-td>
            <yx-td>{{item.intercept | interceptionTypeFilter}}</yx-td>
            <yx-td class="width-80">
              <div class="btn-link" ng-click="detail(item)">查看</div>
              <a href="javascript:;" ng-click="showKefuRemark(item)">{{item.remark ? '查看备注':'客服备注'}}</a>
              <div class="btn-link" href="javascript:;" ng-click="showAfterSale(item)">售后轨迹</div>
            </yx-td>
            </tr>
          </yx-table>
        <div>
          <button class="btn btn-primary" perm-btn="509" ng-click="export()">导出excel</button>
        </div>
      </div>
    </div>
  </div>
</div>