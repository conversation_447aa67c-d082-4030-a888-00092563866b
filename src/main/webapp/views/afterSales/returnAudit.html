<div class="row">

  <div class="col-sm-12">
    <div class="m-card">
      <div class="card-head">
        <span>退款
          <span ng-show="role == 0">客服</span>
          <span ng-show="role == 1">财务</span>审核</span>
      </div>
      <div class="card-body">
        <div class="list-container">
          <!-- 横向滚动条 -->
          <div class="table-wrap" style="overflow: visible;">
            <form class="form form-horizontal" form-Validator="ReturnAuditCtrl" name="formValidator">
              <div class="col-sm-8" style="margin-bottom: 10px;">
                <div class="control-label pull-left" style="line-height: 34px;padding-top: 0;">起止时间：</div>
                <div class="form-group">
                  <div class="col-sm-5">
                    <div class="input-group date">
                      <input name="datetimepicker1" ng-refenddate="datetimepicker2" datetimepicker
                        ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}" ng-model="startTime" type='text' class="form-control"
                        datetimecheck return-type="milliseconds" required />
                      <span class="input-group-addon">
                        <span class="icon icon-calendar"></span>
                      </span>
                    </div>
                  </div>
                  <p class="text-center pull-left" style="line-height: 34px; margin: 0 10px;">至</p>
                  <div class="col-sm-5">
                    <div class="input-group date">
                      <input name="datetimepicker2" ng-refbegindate="datetimepicker1" datetimepicker
                        ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}" ng-model="endTime" type='text' class="form-control"
                        datetimecheck return-type="milliseconds" required />
                      <span class="input-group-addon">
                        <span class="icon icon-calendar"></span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-4" style="margin-bottom: 10px;">
                <div class=" control-label pull-left" style="line-height:34px;padding-top: 0;">
                  订单号：
                </div>
                <div class="pull-left">
                  <input type="text" class="form-control" ng-model="orderId">
                </div>
                <button type="submit" class="btn btn-primary" form-Submit="getList(1)">查询</button>
              </div>
            </form>
            <table class="table table-condensed m-plist" style="margin-bottom:0;border-collapse: collapse;">
              <!-- 搜索区域 -->
              <thead>
                <tr>
                  <th style="line-height:34px;">
                    <input type="checkbox" ng-model="selectAllItem" ng-click="selectAll()">
                  </th>
                  <th style="width:25%">
                    <div class="control-label pull-left" style="line-height:34px;">
                      退款类型：
                    </div>
                    <div class="pull-left">
                      <select class="form-control" ng-model="refundType"
                        ng-options="status[0] as status[1] for status in REFUND_TYPE" ng-change="getList(1)">
                      </select>
                    </div>
                  </th>
                  <th style="width:25%">
                    <div class=" control-label pull-left" style="line-height:34px;">
                      退款渠道：
                    </div>
                    <div class="pull-left">
                      <select class="form-control" ng-model="refundChannel"
                        ng-options="status[0] as status[1] for status in REFUND_CHANNEL" ng-change="getList(1)">
                      </select>
                    </div>
                  </th>
                  <th style="width:25%">
                    <div class="control-label pull-left" style="line-height:34px;">
                      是否极速售后：
                    </div>
                    <div class="pull-left">
                      <select class="form-control" ng-model="speedType"
                        ng-options="speed[0] as speed[1] for speed in speedTypeList" ng-change="getList(1)">
                      </select>
                    </div>
                  </th>
                  <th style="text-align:left">
                    <button type="submit" class="btn btn-success" ng-click="audit(1)">批量审核通过</button>
                    <button type="submit" class="btn btn-danger" ng-click="audit(0)">批量审核不通过</button>
                  </th>
                </tr>
              </thead>



              <tbody ng-show="items.length > 0" ng-repeat="item in items"
                style="border-bottom:3px solid #6493AF;border-top:3px solid #6493AF;">
                <tr>
                  <td>
                    <input type="checkbox" ng-model="item.selectItem" ng-checked="item.selectItem">
                  </td>
                  <td style="text-align:left;height:42px">订单号：
                    <strong>{{item.orderId}}</strong>
                  </td>
                  <td style="text-align:left">订单已退金额：
                    <strong>{{item.refundedSum||0 | price}}</strong>
                  </td>
                  <td style="text-align:left">订单支付金额：
                    <strong>{{item.payTotalSum||0 | price}}</strong>
                    <strong ng-if="item.payTotalPointSum">{{item.payTotalPointSum}}积分</strong>
                  </td>
                  <td style="text-align:left;width:280px;word-wrap:break-word;word-break:break-all;border-top:none;">
                    关联退款申请单：
                    <span ng-repeat="auditId in item.refundmentAuditIdList track by $index">
                      <a href="#/return/search?id={{auditId}}&startTime=&endTime=" target="_blank">{{auditId}}</a>
                      <i ng-hide="$last">,&nbsp;</i>
                    </span>
                  </td>
                </tr>
                <tr>
                  <td style="text-align:left"></td>
                  <td style="text-align:left;height:42px">本次退款金额：
                    <strong>{{item.thisRefundItemSum||0 | price}}</strong>
                    <strong ng-if="item.thisRefundItemPointSum">{{item.thisRefundItemPointSum}}积分</strong>
                  </td>
                  <td style="text-align:left">运费承担方：
                    <strong ng-if="item.refundType != 0">
                      {{item.expUndertake||0 |undertake: expUndertakeList}}
                      <span ng-if="item.forwardExpSum">
                        <i ng-hide="item.refundType == 4 && item.expSum == 0">；</i>原订单全部商品退货退回支付运费
                      </span>
                    </strong>
                    <strong ng-if="item.refundType == 0">
                      发货前取消运费原路返回给顾客
                    </strong>
                  </td>
                  <td style="text-align:left">运费：
                    <strong ng-if="item.refundType != 0">
                      <span ng-if="item.expType == 1">直减券</span>
                      <span ng-if="item.expType == 2">礼品卡</span>
                      <span
                        ng-hide="item.refundType == 4 && item.expSum == 0">{{item.expSum||0 | price}}&nbsp;;&nbsp;</span>
                      <span ng-if="item.forwardExpSum">
                        ({{item.forwardExpSum | price}})
                      </span>
                    </strong>
                    <strong ng-if="item.refundType == 0">{{item.forwardExpSum||0 | price}}</strong>
                  </td>
                  <td style="text-align:left">本次退款总额：
                    {{item.thisRefundTotalSumV2}}
                  </td>
                </tr>
                <tr>
                  <td style="text-align:left"></td>
                  <td style="text-align:left;height:42px">退款类型：
                    <strong>{{item.refundType | refundType }}</strong>
                  </td>
                  <td style="text-align:left">退款渠道：
                    <strong>{{item.refundChannel | refundChannel}}</strong>
                  </td>
                  <td style="text-align:left">提交人：
                    <strong>{{item.submitter}}</strong>
                  </td>
                  <td style="text-align:left">提交时间：
                    <strong ng-show="item.submitTime!=0">{{item.submitTime | date : 'yyyy-MM-dd HH:mm'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>
                    <button type="button" class="btn btn-primary btn-xs"
                      style="line-height:1.42857;width:21px;height:21px;padding:0"
                      ng-click="showDetail(item.id,$event)">-</button>
                  </td>
                  <td style="text-align:left;height:42px">
                    <span ng-show="item.refundType == 3">退款</span>
                    <span ng-hide="item.refundType==3">取消/退货</span>原因：
                    <strong
                      ng-hide="item.refundType==1 || item.refundType==2 || item.refundType == 6">{{item.reason}}</strong>
                  </td>
                  <td style="text-align:left">原因描述：
                    <strong
                      ng-hide="item.refundType==1 || item.refundType==2 || item.refundType == 6">{{item.reasonDesc}}</strong>
                  </td>
                  <td style="text-align:left">扣减备注：
                    <strong>{{item.abatementDesc}}</strong>
                  </td>
                  <td style="text-align:left">
                    <button type="submit" class="btn btn-success"
                      ng-click="audit(1,item.orderId,item.id,item)">审核通过</button>
                    <button type="submit" class="btn btn-danger"
                      ng-click="audit(0,item.orderId,item.id,item)">审核不通过</button>
                  </td>
                </tr>
                <tr id="refundItem{{item.id}}">
                  <td colspan="5">

                    <table class="table table-bordered" style="margin-bottom:10px" ng-show="item.refundType != 7">
                      <thead>
                        <tr>
                          <th style="width:20%">货品名称</th>
                          <th>退货原因</th>
                          <th>收货质检反馈</th>
                          <th style="width:20%">规格</th>
                          <th>SKUID</th>
                          <th>数量</th>
                          <th>单价（元）</th>
                          <th>小计（元）</th>
                          <th ng-hide="item.refundType == 3">实付（元）</th>
                          <th>最大可退金额</th>
                          <th ng-hide="item.refundType == 3">扣减（元）</th>
                          <th ng-hide="item.refundType == 3">应退（元）</th>
                          <th ng-show="item.refundType == 3">退款数量</th>
                          <th ng-show="item.refundType == 3"> 退款金额</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr ng-repeat="refundItem in item.refundItemVOList">
                          <td ng-class="{'bluetext':refundItem.couponAbatement>0,'redtext':refundItem.offlineRefund}">
                            <span ng-if="refundItem.isGift">【赠品】</span>
                            {{refundItem.name}}
                            <span ng-if="refundItem.offlineRefund && refundItem.offlineRefundment>0"
                              style="margin-left: 5px;">（应退{{refundItem.offlineRefundment}}元）</span>
                          </td>
                          <td>
                            <a class="btn btn-link" href="javascript:void(0)" ng-if="refundItem.type != 7"
                              ng-click="showReason(refundItem)">退货原因</a>
                          </td>
                          <td>
                            <div ng-repeat="operatorSKU in refundItem.operateSkus">
                              <div class="btn-link" ng-if="operatorSKU.qualityList"
                                ng-click="checkQualityReport(operatorSKU)">查看收货质检</div>
                            </div>
                          </td>
                          <td ng-class="{'bluetext':refundItem.couponAbatement>0}">{{refundItem.spec}}</td>
                          <td ng-class="{'bluetext':refundItem.couponAbatement>0}">{{refundItem.skuId}}</td>
                          <td ng-class="{'bluetext':refundItem.couponAbatement>0}">{{refundItem.skuNum}}</td>
                          <td ng-class="{'bluetext':refundItem.couponAbatement>0}">
                            {{refundItem.unitPrice|price: refundItem.type==6}}</td>
                          <td ng-class="{'bluetext':refundItem.couponAbatement>0}">
                            {{refundItem.subtotalPrice|price: refundItem.type==6}}</td>
                          <td ng-hide="item.refundType == 3" ng-class="{'bluetext':refundItem.couponAbatement>0}">
                            {{refundItem.actualPrice|price: refundItem.type==6}}</td>
                          <td ng-class="{'bluetext':refundItem.couponAbatement>0}">
                            {{refundItem.maxRefundBalance|price: refundItem.type==6}}</td>
                          <td ng-hide="item.refundType==3" ng-class="{'bluetext':refundItem.couponAbatement>0}">
                            {{(refundItem.couponAbatement>0 ? refundItem.couponAbatement:refundItem.refundAbatement)|price: refundItem.type==6}}
                          </td>
                          <td ng-hide="item.refundType==3" ng-class="{'bluetext':refundItem.couponAbatement>0}">
                            <span
                              ng-if="item.refundType!=8">{{(refundItem.maxRefundBalance-(refundItem.couponAbatement>0 ? refundItem.couponAbatement : refundItem.refundAbatement))|price: refundItem.type==6}}</span>
                            <span ng-if="item.refundType==8">{{refundItem.refundSum|price: refundItem.type==6}}</span>
                          </td>
                          <td ng-show="item.refundType == 3">{{refundItem.refundNum}}</td>
                          <td ng-show="item.refundType == 3">{{refundItem.refundSum|price: refundItem.type==6}}</td>
                        </tr>
                      </tbody>
                    </table>

                    <!--增加换货的商品信息-->
                    <table class="table table-bordered" style="margin-bottom:10px" ng-show="item.refundType == 7">
                      <thead>
                        <tr>
                          <th class="max-width-200">换货商品</th>
                          <th>换货原因</th>
                          <th style="width:20%">收货质检反馈</th>
                          <th class="max-width-200">规格</th>
                          <th>SKUID</th>
                          <th>数量</th>
                          <th>单价(元)</th>
                          <th>实付</th>
                      </thead>
                      <tbody>
                        <tr ng-repeat="applyItem in item.skuInfos">
                          <td>
                            {{applyItem.itemName}}
                          </td>
                          <td>
                            <a class="btn btn-link" href="javascript:void(0)"
                              ng-click="backSKUShowReason(applyItem)">换货原因</a>
                          </td>
                          <td>
                            <div ng-repeat="operatorSKU in applyItem.operateSkus">
                              <div class="btn-link" ng-if="operatorSKU.qualityList"
                                ng-click="checkQualityReport(operatorSKU)">查看收货质检</div>
                            </div>
                          </td>
                          <td class="max-width-200">{{applyItem.skuName}}</td>
                          <td>{{applyItem.skuId}}</td>
                          <td>{{applyItem.count}}</td>
                          <td>{{applyItem.originalPrice|price: applyItem.type==6}}</td>
                          <td>{{(applyItem.count*applyItem.actualPrice)|price: applyItem.type==6}}</td>
                        </tr>
                      </tbody>
                    </table>

                    <!--增加换货的商品信息-->
                    <table class="table table-bordered" ng-show="item.refundType == 7">
                      <thead>
                        <tr>
                          <th style="width:20%">换货回寄商品</th>
                          <th style="width:20%">规格</th>
                          <th>SKUID</th>
                          <th>数量</th>
                          <th>单价（元)</th>
                          <th>回寄订单号</th>
                      </thead>
                      <tbody>
                        <tr ng-repeat="backItem in item.sendBackSkuInfos">
                          <td>
                            <span ng-if="backItem.isGift">【赠品】</span>
                            {{backItem.itemName}}
                          </td>
                          <td>{{backItem.skuName}}</td>
                          <td>{{backItem.skuId}}</td>
                          <td>{{backItem.count}}</td>
                          <td>{{backItem.originalPrice|price: backItem.type==6}}</td>
                          <td>{{item.subOrderId}}</td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>

              <!-- 没有结果 -->
              <tbody ng-show="items.length == 0">
                <tr>
                  <td colspan="11">没有查询到任何数据</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="row">
            <div class="col-sm-11 text-right">
              <!-- 分页数据 -->
              <pagination total-items="pagination.total" items-per-page="pagination.size" ng-model="pagination.page"
                max-size="5" class="pagination-sm" boundary-links="true" rotate="false"
                ng-change="getList(pagination.page);" previous-text="{{'previous'|commoni18n}}"
                next-text="{{'next'|commoni18n}}" first-text="{{'first'|commoni18n}}" last-text="{{'last'|commoni18n}}">
              </pagination>
              <!-- 分页数据 -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>