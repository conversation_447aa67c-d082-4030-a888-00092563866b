
    <div class="row">
    <div class="col-sm-12">
        <div class="m-card">
            <div class="card-head">
                <span>手动确认收货日志</span>
            </div>
            <div class="card-body" style="overflow: hidden;">

              <div class="search-form-wrap">
                <form class="form form-horizontal search-form well" form-Validator="ServiceConfirmLogCtrl" name="formValidator">
                    <div class="form-group form-group-sm">
                        <label class="col-sm-2 control-label">
                            操作时间：
                        </label>
                        <div class="col-sm-4">
                            <div class="input-group date">
                                <input name="datetimepicker1" ng-refenddate="datetimepicker2" datetimepicker ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}"
                                    ng-model="search.startTime" type='text' class="form-control" datetimecheck return-type="milliseconds" required />
                                <span class="input-group-addon"><span class="icon icon-calendar"></span>
                                </span>
                            </div>
                        </div>
                        <p class="text-block col-sm-1 text-center">至</p>
                        <div class="col-sm-4">
                            <div class="input-group date">
                                <input name="datetimepicker2" ng-refbegindate="datetimepicker1" datetimepicker ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}"
                                    ng-model="search.endTime" type='text' class="form-control" datetimecheck return-type="milliseconds" required />
                                <span class="input-group-addon"><span class="icon icon-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-2 control-label">
                            操作人：
                        </label>
                        <div class="col-sm-3">
                            <input type="text" class="form-control" name="orderId" ng-model="search.operator">
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <label class="col-sm-2 control-label">
                            操作原因：
                        </label>
                        <div class="col-sm-3">
                            <select ng-model="search.confirmType" class="form-control" name="operateType" ng-options="type[0] as type[1] for type in comfirmTypeList">
                            </select>
                        </div>
                    </div>
                    <div class="form-group form-group-sm">
                        <div class="col-sm-offset-2 col-sm-9">
                          <button type="submit" class="btn btn-primary" form-Submit="doSearch()">查询</button>
                        </div>
                    </div>
                </form>
              </div>

              <yx-table border="false" pg = "false"
                td-group="['操作时间', '渠道', '售后申请单号','操作原因','操作人']">
                <yx-tr ng-repeat="item in result">
                    <yx-td>{{item.operateTime | date:'yyyy-MM-dd HH:mm:ss'}}</yx-td>
                    <yx-td>{{item.channel}}</yx-td>
                    <yx-td>{{item.applyId}}</yx-td>
                    <yx-td>{{item.reason}}</yx-td>
                    <yx-td>{{item.userName}}</yx-td>
                </yx-tr>
              </yx-table>

              <div>
                <button type="button" class="btn btn-info" ng-click="exportExcel()">导出操作明细</button>
              </div>
              <div style="float:right;">
                    <pagination total-items="pagination.total" items-per-page="pagination.size" ng-model="pagination.page"
                    max-size="5" class="pagination-sm" boundary-links="true" rotate="false" ng-change="pageChanged(pagination.page, search);"
                    previous-text="{{'previous'|commoni18n}}" next-text="{{'next'|commoni18n}}" first-text="{{'first'|commoni18n}}"
                    last-text="{{'last'|commoni18n}}"></pagination>
              </div>
            </div>
        </div>
    </div>
</div>
