<div class="row">

  <div class="col-sm-12">
    <div class="m-card">
      <div class="card-head">
        <span>退货列表</span>
      </div>
      <div class="card-body">
        <div class="list-container">
          <div class="search-form-wrap">
            <form class="form form-horizontal search-form well" form-Validator="returnCtrl" name="formValidator">
              <div class="form-group form-group-sm">
                <label class="col-sm-2 control-label">
                  更新时间：
                </label>
                <div class="form-group" style="float:left;">
                  <div class="pull-left">
                    <div class="input-group date">
                      <input name="datetimepicker1" ng-refenddate="datetimepicker2" datetimepicker
                        ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}" ng-model="search.startTime" type='text'
                        class="form-control" datetimecheck return-type="milliseconds" required />
                      <span class="input-group-addon"><span class="icon icon-calendar"></span>
                      </span>
                    </div>
                  </div>
                  <p class="text-center pull-left" style="line-height: 34px; margin: 0 10px;">至</p>
                  <div class=" pull-left" >
                    <div class="input-group date">
                      <input name="datetimepicker2" ng-refbegindate="datetimepicker1" datetimepicker
                        ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}" ng-model="search.endTime" type='text'
                        class="form-control" datetimecheck return-type="milliseconds" required />
                      <span class="input-group-addon"><span class="icon icon-calendar"></span>
                      </span>
                    </div>
                  </div>
                </div>
                <label class=" col-sm-1 control-label" style="margin-left: 5%;">
                  用户身份：
                </label>
                <div >
                  <select ng-model="search.userCrmType" class=" form-control" style="width: 10%;height: 37px;" >
                    <option value="-1">全部</option>
                    <option value="1">超级VIP</option>
                  </select>
                </div>               
              </div>
              <div class="form-group form-group-sm">
                <label class="col-sm-2 control-label">
                  订单号：
                </label>
                <div class="col-sm-3">
                  <input type="text" class="form-control" name="orderId" ng-model="search.orderId" postiveint>
                </div>
                <div ng-init="search.mode = 1" >
                  <select ng-model="search.mode" class="col-sm-1 form-control" style="margin-left: 170px;width: 100px">
                    <option value="1">商品ID</option>
                    <option value="2">SKUID</option>
                  </select>
                </div>
                <div class="col-sm-3" style="margin-left: 10px">
                  <input type="text" class="form-control" name="otherId" ng-model="search.otherId" postiveint>
                </div>
              </div>

              <div class="form-group form-group-sm">
                <label class="col-sm-2 control-label">
                  退货申请单号：
                </label>
                <div class="col-sm-3">
                  <input type="text" class="form-control" name="id" ng-model="search.id" postiveint>
                </div>
                <label class="col-sm-offset-1 col-sm-2 control-label">
                  是否极速售后
                </label>
                <div class="col-sm-3" style="margin-left: 10px">
                  <select class="form-control" ng-model="search.speedType"
                    ng-options="speed[0] as speed[1] for speed in speedTypeList">
                  </select>
                </div>
              </div>
              <div class="form-group form-group-sm">
                <label class="col-sm-2 control-label">
                  物流单号：
                </label>
                <div class="col-sm-3">
                  <input type="text" class="form-control" name="trackNum" ng-model="search.trackNum">
                </div>
                <label class="col-sm-offset-1 col-sm-2 control-label">
                  是否上门取件
                </label>
                <div class="col-sm-3" style="margin-left: 10px">
                  <select class="form-control" ng-model="search.sendBackWay"
                    ng-options="sendbackway[0] as sendbackway[1] for sendbackway in SendBackWayList">
                  </select>
                </div>
              </div>
              <div class="form-group form-group-sm">
                <label class="col-sm-2 control-label">
                  按状态：
                </label>
                <div class="col-sm-3">
                  <select class="form-control" ng-model="search.status"
                    ng-options="status[0] as status[1] for status in SEARCH_STATUSES">
                  </select>
                </div>
                <label class="col-sm-2 control-label">收货时长监控</label>
                <div class="col-sm-3" style="margin-left: 10px">
                  <select class="form-control" ng-model="search.receiptMonitorType"
                    ng-options="receiptMonitor[0] as receiptMonitor[1] for receiptMonitor in receiptMonitorTypeList"></select>
                </div>
              </div>
              <div class="form-group form-group-sm">
                <label class="col-sm-2 control-label">按退回仓库：</label>
                <div class="col-sm-3">
                  <select class="form-control" ng-model="search.skuReturnStoreHouseType"
                    ng-options="storeHouse[0] as storeHouse[1] for storeHouse in returnStoreHouse"></select>
                </div>
              </div>
              <div class="form-group form-group-sm">
                <div class="col-sm-offset-2 col-sm-9">
                  <button type="submit" class="btn btn-primary" form-Submit="handleQuery(search)">查询</button>
                </div>
              </div>
            </form>
          </div>
          <!-- 横向滚动条 -->
          <div class="table-wrap">
            <table class="table table-condensed m-plist">
              <!-- 可排序的thead -->
              <thead>
                <tr>
                  <th>更新时间</th>
                  <th>用户名 </th>
                  <th>订单号</th>
                  <th>退货申请单号</th>
                  <th>联系人</th>
                  <th>电话 </th>
                  <th>退货商品</th>
                  <!-- <th>退款金额<br /><span class="f-12">(元/积分)</th> -->
                  <th>退货物流单号</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody ng-show="items.length == 0">
                <tr>
                  <td colspan="11">没有查询到任何数据</td>
                </tr>
              </tbody>
              <tbody ng-show="items.length > 0" ng-repeat="item in items">
                <tr>
                  <td>{{item.updateTime | date : 'yyyy-MM-dd HH:mm:ss'}}</td>
                  <td>
                    <span>{{item.userName}}</span>
                    <div>
                      <span ng-repeat="label in item.userLabels">
                        <span ng-if="label.tip" tooltip="{{label.tip}}"
                          ng-class="{'credit-link': isCreditLink(label.desc)}"
                          ng-click="openLink(label.desc, item.userId)" tooltip-placement="top" class="label"
                          ng-style="{'background': label.color}">{{label.desc}}</span>
                        <span ng-if="!label.tip" class="label" ng-class="{'credit-link': isCreditLink(label.desc)}"
                          ng-click="openLink(label.desc, item.userId)"
                          ng-style="{'background': label.color}">{{label.desc}}</span>
                      </span>
                    </div>
                  </td>
                  <td><a href="#/search/orderDetail/{{item.orderId}}" target="_blank">{{item.orderId}}</a></td>
                  <td>
                    <div>{{item.id}}</div>
                    <div class="label f-block f-m-t-5" ng-repeat="tag in item.userTags"
                      ng-style="{'background': tag.color}">{{tag.desc}}</div>
                  </td>
                  <td>{{item.name}}</td>
                  <td>
                    <span copy-phone phone="{{item.mobile}}">{{item.mobile}}</span>
                  </td>
                  <td class="max-width-200">
                    <div ng-repeat="sku in item.skuList">
                      {{sku.itemNickName || sku.name}} {{sku.specValues}}x{{sku.count}} {{sku.promotionList}}
                      <div ng-repeat="tag in sku.customTagList">
                        <span ng-style="{'backgroundColor': tag.bgColor, 'color': tag.textColor}" class="label">{{tag.tagName}}</span>
                      </div>
                    </div>
                  </td>
                  <!-- <td>
                    {{item.amount | price}}
                    <div ng-if="item.refundPointSum">{{item.refundPointSum}}积分</div>
                  </td> -->
                  <td>
                    <div>
                      <a ng-if="item.trackingNum" href="javascript:;"
                        ng-click="showReturnDelivery(item.expressCompany,item.trackingNum, item.id)">
                        {{item.expressCompany}}<br />{{item.trackingNum}}
                      </a>
                      <span ng-if="item.returnTrackNumDupFlag" class="label label-danger">重复</span>
                    </div>
                    <a href="javascript:;" ng-if="item.createTicketQuickly" ng-click="getRepetitionInfo(item)">催促质检</a>
                  </td>
                  <td>
                    <span>
                      {{item.status | returnStatus}}
                    </span>
                  </td>
                  <td class="width-80">
                    <div>
                      <a ng-click="showReturnDetail(item)" href="javascript:;">
                        <span ng-if="item.status != 1 && item.status !=9">
                          查看
                        </span>
                        <span ng-if="item.status == 9">
                          客服确认
                        </span>
                        <span ng-if="item.status == 1">
                          审核
                        </span>
                      </a>
                      <br />
                      <a href="javascript:;" ng-click="showKefuRemark(item)">{{item.returnRemark ? '修改备注':'客服备注'}}</a>
                      <br />
                      <a href="javascript:;" ng-click="showAfterSale(item)">售后轨迹</a>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="row">
            <form action="/return/export" method="post" style="float: left" id="form">
              <input type="hidden" name="startTime" value="{{search.startTime || 0}}">
              <input type="hidden" name="endTime" value="{{search.endTime || 0}}">
              <input type="hidden" name="orderId" value="{{search.orderId || 0}}">
              <input type="hidden" name="id" value="{{search.id || 0}}">
              <input type="hidden" name="status" value="{{search.status == 100 ? 4 : search.status}}">
              <input type="hidden" name="trackNum" value="{{search.trackNum}}">
              <input type="hidden" name="itemId" value="{{search.mode == 1 && search.otherId ? search.otherId : -1}}">
              <input type="hidden" name="skuId" value="{{search.mode == 2 && search.otherId ? search.otherId : -1}}">
              <input type="hidden" name="speedType" value="{{search.speedType}}">
              <input type="hidden" name="sendBackWay" value="{{search.sendBackWay}}">
              <!-- <input class="btn btn-success" type="submit" perm-btn="50201" value="导出excel"> -->
            </form>
            <button class="btn btn-success" style="float:left" perm-btn="50201"
              ng-click="returnListExport()">导出excel</button>

            <button class="btn btn-primary f-margin-left-10" perm-btn="502007" style="float: left"
              ng-click="exportPickTask()">导出大件取件任务</button>
            <button class="btn btn-primary f-margin-left-10" perm-btn="502008" style="float: left"
              ng-click="bindPickNumber()">绑定大件取件单号</button>

            <div style="float: right">
              <!-- 分页数据 -->
              <pagination total-items="pagination.total" items-per-page="pagination.size" ng-model="pagination.page"
                max-size="5" class="pagination-sm" boundary-links="true" rotate="false"
                ng-change="pageChanged(pagination.page, search);" previous-text="{{'previous'|commoni18n}}"
                next-text="{{'next'|commoni18n}}" first-text="{{'first'|commoni18n}}" last-text="{{'last'|commoni18n}}">
              </pagination>
              <!-- 分页数据 -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>