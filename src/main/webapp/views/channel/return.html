<div class="row">
    <div class="col-sm-12">
        <div class="m-card">
            <div class="card-head">
                <span>渠道退货列表</span>
            </div>
            <div class="card-body">

                <div class="search-form-wrap">
                    <form class="form form-horizontal search-form well" form-Validator="returnCtrl"
                        name="formValidator">
                        <div class="form-group form-group-sm">
                            <label class="col-sm-2 control-label">
                                <select class="j-label control-label" ng-model="timeType">
                                    <option value="1">申请时间</option>
                                    <option value="2">更新时间</option>
                                </select>
                            </label>
                            <div class="form-group" style="float:left;width: 80%;">
                                <div class="col-sm-5">
                                    <div class="input-group date">
                                        <input name="datetimepicker1" ng-refenddate="datetimepicker2" datetimepicker
                                            ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}" ng-model="timeGroup.startTime"
                                            type='text' class="form-control" datetimecheck return-type="milliseconds" required />
                                        <span class="input-group-addon"><span class="icon icon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                                <p class="text-block col-sm-1 text-center">至</p>
                                <div class="col-sm-5">
                                    <div class="input-group date">
                                        <input name="datetimepicker2" ng-refbegindate="datetimepicker1" datetimepicker
                                            ng-dtoptions="{format:'YYYY-MM-DD HH:mm:ss'}" ng-model="timeGroup.endTime"
                                            type='text' class="form-control" datetimecheck return-type="milliseconds" required />
                                        <span class="input-group-addon"><span class="icon icon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-2 control-label">
                                订单号：
                            </label>
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="orderId" ng-model="search.orderId">
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-2 control-label">
                                退货申请单号：
                            </label>
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="id" ng-model="search.applyId">
                            </div>
                            <label class="col-sm-2 control-label">
                                <select class="j-label control-label" ng-model="searchType"
                                    ng-change="searchTypeChange()">
                                    <option value="1">商品ID</option>
                                    <option value="2">SKUID</option>
                                </select>
                            </label>
                            <div class="col-sm-3">
                                <input type="text" ng-model="searchKey" class="form-control">
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-2 control-label">
                                物流单号：
                            </label>
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="trackNum" ng-model="search.trackingNum">
                            </div>
                            <label class="col-sm-offset-1 col-sm-2 control-label">
                                按渠道：
                            </label>
                            <div class="col-sm-3">
                                <div multi-select all-option="true" selected-List="channelIds" group="channelList"
                                    ngf-options="{nonSelectedText:'全部',isCollapsible:false}"></div>
                                <!-- <search-select list="channelList" ng-model="search.channelId" init-value="全部"/> -->
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <label class="col-sm-2 control-label">
                                按状态：
                            </label>
                            <div class="col-sm-3">
                                <select class="form-control" ng-model="search.statusList"
                                    ng-options="status[0] as status[1] for status in SEARCH_STATUSES">
                                </select>
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <div class="col-sm-offset-2 col-sm-9">
                                <button type="submit" class="btn btn-primary" form-Submit="doSearch(search)">查询</button>
                            </div>
                        </div>
                    </form>
                </div>

                <yx-table border="false" pg="true"
                          td-group="['申请时间', '更新时间', '用户名', '渠道','订单号','退货申请单号','联系人','电话','退货商品/SKUID', '退货物流单号','状态','操作']">
                    <yx-tr ng-repeat="item in result">
                        <yx-td>{{item.createTime | date:'yyyy-MM-dd HH:mm:ss'}}</yx-td>
                        <yx-td>{{item.updateTime | date:'yyyy-MM-dd HH:mm:ss'}}</yx-td>
                        <yx-td>{{item.userName}}</yx-td>
                        <yx-td>{{item.channelName}}</yx-td>
                        <yx-td><a href="#/search/otherorderDetail/{{item.orderId}}/{{item.orderChannel}}"
                                target="_blank">{{item.orderId}}</a></yx-td>
                        <yx-td>{{item.applyId}}</yx-td>
                        <yx-td>{{item.userName}}</yx-td>
                        <yx-td>
                            <span copy-phone phone="{{item.contactMobile}}">{{item.contactMobile}}</span>
                        </yx-td>
                        <yx-td class="max-width-200">
                            <div ng-repeat="sku in item.applySkuList">
                                <span>{{sku.itemName}}*{{sku.count}} {{sku.skuId}}</span>
                            </div>
                        </yx-td>
                        <yx-td>
                            <div ng-repeat="express in item.expressInfoList">
                                <a href="javascript:;"
                                    ng-click="showReturnDelivery(express.trackingCompany,express.trackingNum, express.applyId)">
                                    {{express.trackingCompany}} {{express.trackingNum}}
                                </a>
                            </div>
                        </yx-td>
                        <yx-td>{{item.status | returnStatus}}</yx-td>
                        <yx-td>
                            <div class="btn-link" ng-click="aduit(item)">
                                <span ng-if="item.status != 1 && item.status != 9">
                                    查看
                                </span>
                                <span ng-if="item.status == 9">
                                    客服确认
                                </span>
                                <span ng-if="item.status == 1">
                                    审核
                                </span>
                            </div>
                            <div class="btn-link" ng-click="remark(item)">{{item.remark ? '修改备注' : '客服备注'}}</div>
                            <!-- 售后暂不支持，之后添加 -->
                            <div class="btn-link" ng-click="showAfterSale(item)">售后轨迹</div>
                        </yx-td>
                    </yx-tr>
                </yx-table>

                <div>
                    <button type="button" class="btn btn-info" ng-click="export()">导出execl</button>
                </div>
            </div>
        </div>
    </div>
</div>