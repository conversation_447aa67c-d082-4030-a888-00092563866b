'use strict';

angular.module('adminApp')
  .constant('RETURN_SEARCH_STATUSES', [
    [0, '全部'],
    [1, '待审核'],
    [2, '待用户寄回商品'],
    [3, '待仓库收货质检'],
    [4, '退货完成'],
    [17, '待修改申请'],
    [5, '审核不通过'],
    [6, '用户取消'],
    [7, '系统取消'],
    [8, '客服取消'],
    [9, '待客服确认'],
    [11, '客服拒绝'],
    [15, '待服务商拆卸商品'],
  ])
  .controller('ReturnListCtrl',
    function ($scope, returnService, $modal, dialog, toastr, validator, $route, RETURN_SEARCH_STATUSES, RECEIPT_STATUS_LIST, afterSalesService, QUICK_AFTERSALE, SEND_BACK_WAY, RETURN_STORE_HOUSE, RECEIPT_MONITOR_TYPE, common, toolService) {
      'ngInject';

      // 默认设置时间区间为现在到过去7天
      var rangeDate = Tool.initRangeDate(6);
      var startTime = rangeDate.startTime;
      var endTime = rangeDate.endTime;

      $scope.SEARCH_STATUSES = RETURN_SEARCH_STATUSES; // 审核通过，直接退款状态特殊处理

      // 快递签收状态
      $scope.receiptStatusList = RECEIPT_STATUS_LIST;

      $scope.speedTypeList = QUICK_AFTERSALE;

      $scope.SendBackWayList = SEND_BACK_WAY;

      $scope.returnStoreHouse = RETURN_STORE_HOUSE;
      $scope.receiptMonitorTypeList = RECEIPT_MONITOR_TYPE;

      // 防抖
      $scope.ajaxLoading = false;

      // 初始化默认搜索值
      $scope.search = {
        status: 0,
        startTime: startTime,
        endTime: endTime,
        speedType: -1,
        sendBackWay: -1,
        skuReturnStoreHouseType: 0,
        receiptMonitorType: 0,
        userCrmType: -1
      };

      if ($route.current.params.applyid) {
        $scope.search.id = $route.current.params.applyid;
        $scope.search.startTime = '';
        $scope.search.endTime = '';
      }

      if ($route.current.params.orderId) {
        $scope.search.orderId = $route.current.params.orderId;
      }

      validator.setRules({
        orderId: {
          postiveint: '订单号必须是正整数'
        },
        id: {
          postiveint: '退款申请单号必须是正整数'
        },
        datetimepicker1: {
          required: '开始时间不能为空'
        },
        datetimepicker2: {
          required: '结束时间不能为空'
        }
      });


      // 刷新当前页
      function refreshCurrentPage() {
        queryAll($.extend({}, $scope.search, { page: $scope.pagination.page }));
      }

      function queryAll(search) {
        var _search = {};
        var key;
        var params = {};

        for (key in search) {
          if (!!search[key] || search[key] === 0) {
            _search[key] = search[key];
          }
        }

        if (!_search.startTime) {
          toastr.error('开始时间不能为空');
          return;
        }
        if (!_search.endTime) {
          toastr.error('结束时间不能为空');
          return;
        }

        // 检查时间范围限制（最大31天）
        if (_search.startTime && _search.endTime && !_search.id && !_search.orderId && !_search.trackNum) {
          var timeDiff = _search.endTime - _search.startTime;
          var maxDays = 31;
          var maxTime = maxDays * 24 * 60 * 60 * 1000; // 31天的毫秒数
          if (timeDiff > maxTime) {
            toastr.error('搜索时间范围不能超过31天');
            return;
          }
        }

        // 审核通过，并直接退款查询条件处理
        if (_search.status == 100) {
          _search.status = 4;
        } else {
        }

        params = angular.extend({}, _search);
        if (params.id) {
          params.startTime = 0;
          params.endTime = 0;
        }

        if (params.mode == 1) {
          params.itemId = params.otherId || -1;
        } else {
          params.skuId = params.otherId || -1;
        }
        delete params.mode;
        delete params.otherId;

        returnService.list(params).then(function (data) {
          $scope.items = data.result;
          $scope.pagination = data.pagination;
          //导出查询结果
          $scope.searchForExcel = $.extend(true, {}, params);
        });
      }

      $scope.handleQuery = function (search) {
        queryAll($.extend({}, search, { page: 1 }));
      };

      $scope.pageChanged = function (page, search) {
        queryAll($.extend({}, search, { page: page }));
      };

      // 退货订单详情
      $scope.showReturnDetail = function (item) {
        $modal.open({
          templateUrl: '/views/afterSales/dialog/returnDetail.html',
          controller: 'ReturnDetailCtrl',
          backdrop: 'static',
          size: 'big',
          resolve: {
            item: function () {
              return item;
            },
          }
        }).result.then(function (res) {
          refreshCurrentPage();
        });
      };

      // 信誉标签点击事件
      $scope.openLink = function (label, userId) {
        toolService.openLink(label, userId)
      }

      // 信誉标签hover展示下划线
      $scope.isCreditLink = function (label) {
        return /^R[1|2|3|4|5]{1}$/.test(label);
      }

      // 退货物流
      $scope.showReturnDelivery = function (expressCompany, trackingNum) {
        afterSalesService.showReturnDelivery({
          expressCompany: expressCompany,
          trackingNum: trackingNum
        });
      };

      //
      $scope.showKefuRemark = function (item) {
        afterSalesService.openRemarkDialog({
          id: item.id,
          type: 'return',
          item: item,
          remark: item.returnRemark || { remark: '' },
          callback: function (data) {
            returnService.returnRemark(data.submitParam).then(function () {
              refreshCurrentPage();
            });
          }
        });
      };

      // $scope.cancelApply = function (item) {
      //   dialog.confirm({
      //     title: '取消申请',
      //     content: '是否确定取消用户拒收退款申请？'
      //   }).then(function () {
      //     returnService.cancelApply(item.id, item.orderId).then(function (res) {
      //       refreshCurrentPage();
      //     });
      //   });
      // };

      $scope.showAfterSale = function (item) {
        item.applyId = item.id;
        item.type = 2;
        afterSalesService.showAfterSale({
          item: item,
          userId: item.userId
        });
      };

      $scope.exportPickTask = function () {
        afterSalesService.exportPickTask(2, 3);
      };

      $scope.bindPickNumber = function (item) {
        afterSalesService.bindPickNumber(2);
      };

      queryAll($scope.search);

      // 获取重复建单信息
      $scope.getRepetitionInfo = function(item) {
        var params = {  
          applyId: item.id,
          type: item.type,
          channelId: item.channelId
        }
        returnService.queryByApply(params).then((res) => {
          if (res.ticketList && res.ticketList.length > 0) {
            $scope.confirmRepeatTicket(res, params);
          } else {
            $scope.confirmCreateTicket(params);
          }
        })
        // 添加埋点
        window.YXdss && window.YXdss.specialActionReport({
          YX_APP_STAT_operatorKey: 'customersystemQualityInspection',
          YX_APP_STAT_operatorName: '催促质检',
          YX_APP_STAT_operatorStartTime: new Date().getTime(),
          YX_APP_STAT_operatorEndTime: new Date().getTime()
        });
      }

      // 存在重复工单-展示重复工单弹窗
      $scope.confirmRepeatTicket = function(verifyResult, params) {
        var modalInstance = $modal.open({
          templateUrl: 'views/afterSales/dialog/RepetitionTipModal.html',
          controller: 'RepetitionTipController',
          backdrop: 'static',
          size: 'lg',
          resolve: {
            params: function () {
              return { 
                repetitionData: verifyResult,
              }
            }
          }
        });

        // 弹窗在关闭的时候执行的
        modalInstance.result.then(function (data) {
          console.log('RepetitionTipComponent',data);
          $scope.createTicket(params)
        }, function () {
          console.log('取消建单');
        });
      }

      // 无重复工单-二次弹窗确认
      $scope.confirmCreateTicket = function(params) {
        var modalInstance = $modal.open({
          templateUrl: 'views/afterSales/dialog/createConfirmTicketModal.html',
          controller: 'createConfirmTicketModalCtrl',
          backdrop: 'static',
          size: 'md',
          resolve: {
            params: function () {
              return { 
                title: '创建催促质检工单'
              }
            }
          }
        });
        modalInstance.result.then(function (data) {
          $scope.createTicket(params)
        }, function () {
          console.log('取消建单');
        })
      }

      // 快速建单
      $scope.createTicket = function(params) {
        if ($scope.ajaxLoading) {
          return false;
        }
        $scope.ajaxLoading = true;
        returnService.createQualityTimeoutTicketForce(params).then(function (res) {
          $scope.ajaxLoading = false;
          if (res) {
            toastr.success('工单创建成功');
            refreshCurrentPage();
          }
        }).catch(err => {
          $scope.ajaxLoading = false;
          $scope.retryCreateTicket(params);
        })
      }

      // 重试建单
      $scope.retryCreateTicket = function(params) {
        var modalInstance = $modal.open({
          templateUrl: 'views/afterSales/dialog/retryCreateTicketCommonModal.html',
          controller: 'createConfirmTicketModalCtrl',
          backdrop: 'static',
          size: 'md',
          resolve: {
            params: function () {
              return { }
            }
          }
        });

        // 弹窗在关闭的时候执行的
        modalInstance.result.then(function () {
          console.log('重试建单');
          $scope.createTicket(params)
        }, function () {
          console.log('取消建单');
        });
      }

      $scope.setAction = function () {
        var form = document.getElementById('form');
        form.action = common.contextPath + '/return/export';
      }

      $scope.setAction();

      // 导出excel
      $scope.returnListExport = function () {
        // 检查必填字段：当没有特殊查询条件时，开始时间和结束时间必填
        var hasSpecialCondition = $scope.search.id || $scope.search.orderId || $scope.search.trackNum;
        if (!hasSpecialCondition) {
          if (!$scope.search.startTime) {
            toastr.error('开始时间不能为空');
            return;
          }
          if (!$scope.search.endTime) {
            toastr.error('结束时间不能为空');
            return;
          }
        }

        var params = {
          startTime: $scope.search.startTime || 0,
          endTime: $scope.search.endTime || 0,
          orderId: $scope.search.orderId || 0,
          id: $scope.search.id || 0,
          status: $scope.search.status == 100 ? 4 : $scope.search.status,
          trackNum: $scope.search.trackNum,
          itemId: ($scope.search.mode == 1 && $scope.search.otherId) ? $scope.search.otherId : -1,
          skuId: ($scope.search.mode == 2 && $scope.search.otherId) ? $scope.search.otherId : -1,
          speedType: $scope.search.speedType,
          sendBackWay: $scope.search.sendBackWay,
          skuReturnStoreHouseType: $scope.search.skuReturnStoreHouseType,
          userCrmType : $scope.search.userCrmType
        }
        var url = common.contextPath + '/return/export?' + $.param(params);
        var newWindow = window.open(url, '正在导出。。。');
        newWindow.document.title = '正在导出。。。';
      }
    }

  ).controller('ConfirmRefoundCtrl', ['$scope', '$modalInstance', 'params', '$location', function ($scope, $modalInstance, params, $location) {
    // 关闭
    $scope.transferAccountVO = params.transferAccountVO;
    $scope.cancel = function () {
      // window.location.reload();
      $modalInstance.dismiss();
    };
    // 确定
    $scope.ok = function () {
      $modalInstance.close($scope.transferAccountVO);
    };
  }]);;
