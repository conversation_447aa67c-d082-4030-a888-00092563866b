'use strict';

angular.module('adminApp')
  .controller('ServiceConfirmLogCtrl',
      function($scope, returnService, $modal, toastr, afterSalesService, INIT_PAGINATION, DELIVERY_CONFIRM_TYPE, validator) {
        'ngInject';

        $scope.result = [];

        var rangeDate = Tool.initRangeDate();

        $scope.search = {
            startTime: rangeDate.startTime,
            endTime: rangeDate.endTime,
            operator: '',
            confirmType: 0
        };

        $scope.comfirmTypeList = transformObj(DELIVERY_CONFIRM_TYPE)

        function transformObj(obj) {
          var typeList = [[0, '全部']];
          for (var key in obj) {
              typeList.push([Number(key), obj[key]])
          }
          return typeList;
        }

        $scope.pagination = INIT_PAGINATION;

        // 设置验证规则
        validator.setRules({
            datetimepicker1: {
                required: '开始时间不能为空'
            },
            datetimepicker2: {
                required: '结束时间不能为空'
            }
        });

        $scope.doSearch = function() {
            $scope.pagination.page = 1;
            search($scope.search);
        }

        // 刷新当前页
        function refreshCurrentPage() {
          search($.extend({}, $scope.search, {page: $scope.pagination.page}));
        }

        function search() {
          var _search = {};
          var key;
          var params = {};

          for (key in $scope.search) {
            if (!!$scope.search[key] || $scope.search[key] === 0) {
              _search[key] = $scope.search[key];
            }
          }

          params = angular.extend({}, _search, $scope.pagination);

          returnService.getConfirmDeliveryOperation(params).then(function (data) {
            $scope.result = data.result;
            $scope.pagination = data.pagination;
            //导出查询结果
            // $scope.searchForExcel = $.extend(true, {}, params);
          });
        }

        $scope.handleQuery = function () {
          search($.extend({}, $scope.search, {page: 1}));
        };

        $scope.pageChanged = function (page) {
          search($.extend({}, $scope.search, {page: page}));
        };

        $scope.showAfterSale = function (item) {
          item.applyId = item.id;
          afterSalesService.showAfterSale({
            item: item
          });
        };

        $scope.bindPickNumber = function (item) {
          afterSalesService.bindPickNumber(2);
        };

        search($scope.search);

        function getParams() {
          var params = $.extend({}, $scope.search, {page: $scope.pagination.page, size: $scope.pagination.size});
          return params;
        }
      
          //导出excel
        $scope.exportExcel = function() {
          // 检查必填字段：开始时间和结束时间不可为空
          if (!$scope.search.startTime) {
            toastr.error('开始时间不能为空');
            return;
          }
          if (!$scope.search.endTime) {
            toastr.error('结束时间不能为空');
            return;
          }

          var params = getParams();
          window.open('/yxkf//return/export/kefuConfirmDeliveryOperation.json?' + $.param(params));
        }
      }

  );
