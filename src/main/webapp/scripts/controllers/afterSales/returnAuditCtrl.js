'use strict';

angular.module('adminApp')
  .controller('ReturnAuditCtrl', ['$scope', 'retund', 'returnService', '$modal', 'dialog', 'toastr', 'validator', '$route', 'REFUND_CHANNEL', 'quanlityFeedbackService','QUICK_AFTERSALE', 'userService', '$rootScope',
    function($scope, Retund, returnService, $modal, Dialog, Toastr, Validator, $route, REFUND_CHANNEL, QuanlityFeedbackService, QUICK_AFTERSALE, userService, $rootScope) {

      $scope.role = $route.current.params.role;
      $scope.refundType = -1;
      $scope.refundChannel = -1;
      $scope.orderId = '';
      $scope.speedType = -1;
      $scope.REFUND_TYPE = [
        [-1, '全部'],
        [0, '发货前客服取消'],
        [1, '发货后直接退款'],
        [2, '发货后货到退款'],
        [3, '客服创建直接退款'],
        [4, '发货前包裹取消'],
        [6, '发货后拒收退款'],
        [7, '换货退运费'],
        [8, '价保差价退款'],
        [9, '维修费用退款'],
        [10, '维修商品退款'],
        [17, "积分债务退款"]
      ];
      $scope.expUndertakeList = []; // 枚举运费承担方
      $scope.REFUND_CHANNEL = REFUND_CHANNEL;
      $scope.speedTypeList = QUICK_AFTERSALE;
      $scope.endTime = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1).getTime();
      $scope.startTime = new Date(new Date($scope.endTime).getTime() - 1000 * 60 * 60 * 24 * 90).getTime();

      // 设置验证规则
      Validator.setRules({
        datetimepicker1: {
          required: '开始时间不能为空'
        },
        datetimepicker2: {
          required: '结束时间不能为空'
        }
      });

      // 数据列表  [必须]
      $scope.items = [];
      // 分页数据  [必须]
      $scope.pagination = {
        page: 1,
        size: 10,
        totalPage: 0,
        total: 0
      };
      $scope.confirms = [];
      // 退货原因
      $scope.showReason = function (item) {
        $modal.open({
          animation: true,
          backdrop: 'static',
          templateUrl: '/views/afterSales/dialog/refundReasonModal.html',
          controller: 'RefundReasonModalCtrl',
          resolve: {
            params: function () {
              return {skuInfo: item};
            }
          }
        });
      };
      // 换货原因
      $scope.backSKUShowReason = function (applyItem) {
        var item = applyItem;
        item.name = applyItem.itemName;
        item.spec = applyItem.skuName;
        item.reason = applyItem.applySkuReason.reason;
        item.reasonDesc = applyItem.applySkuReason.reasonDesc;
        item.picList = [];
        applyItem.applyPicList && applyItem.applyPicList.map(function (pic) {
          item.picList.push(pic.picUrl);
        });
        $scope.showReason(item);
      };

      // 查看质检报告
      $scope.checkQualityReport = function (sku) {
        QuanlityFeedbackService.checkQualityReport(sku, sku.returnStoreHouseType);
      };
      // 获取列表
      $scope.getList = function(page) {
        var params = {
          "refundType": $scope.refundType,
          "refundChannel": $scope.refundChannel,
          'speedType': $scope.speedType,
          "orderId": $scope.orderId,
          "role": $scope.role,
          "page": page,
          "size": 10,
          startTime: $scope.startTime,
          endTime: $scope.endTime
        }
        if(!params.orderId){
          delete params.orderId;
        }
        Retund.auditList(params).then(function(data) {
          $scope.items = data.result;
          $scope.pagination.totalPage = data.pagination.totalPage;
          $scope.pagination.total = data.pagination.total;
          $scope.confirms = [];
          $scope.items.forEach(function (item) {
            if (item.reason === '回馈金包退款') {
              $scope.getFfBonusInOutPendDetails(item);
            }
          })
          $("body").scrollTop(0);
        }, function() {
          $scope.items = [];
        });
      };
      function getEnumMapList (params) {
        returnService.getenumMapQuery(params).then(function (data) {
          $scope.expUndertakeList = data.EXP_UNDERTAKE;
        })
      }
      getEnumMapList({ enumCodes: 'EXP_UNDERTAKE' });

      $scope.getList(1);

      $scope.selectAll = function() {
        var i = 0;
        if ($scope.selectAllItem == true) {
          for(i in $scope.items){
            $scope.items[i].selectItem = true;
          }
        } else {
          for(i in $scope.items){
            $scope.items[i].selectItem = false;
          }
        }
      }

      $scope.showDetail = function(id,event){
        var refundItem = $("#refundItem"+id);
        if(refundItem.css("display") != "none"){
          event.currentTarget.innerHTML = "+";
          refundItem.hide();
        }else{
          event.currentTarget.innerHTML = "-";
          refundItem.show();
        }
      }

      // 回馈金包取明细后提交
      $scope.getFfBonusInOutPendDetails = function (item) {
        userService.getFfBonusInOutPendDetails({userId: item.userId, orderId: item.orderId, type: 3}).then(function (res) {
          item.bonusPendVOS = res.bonusPends.result;
          $scope.confirms.push({
            userId: item.userId,
            orderId: item.orderId,
            bonusPendVOS: res.bonusPends.result
          });
        })
      }

      $scope.audit = function(type,orderId,id,item) {
        var auditItems = [],
          checkItems = [],  // 验证申请人和审核人是否相同处理
          confirms = [],
          i = 0;

        if(id == undefined){
          for(i in $scope.items){
            if($scope.items[i].selectItem == true){
                auditItems.push($scope.items[i].id);
                checkItems.push($scope.items[i]);
                $scope.confirms.map(function (item) {
                  if (item.orderId === $scope.items[i].orderId) {
                    confirms.push(item);
                  }
                })
            }
          }
        } else {
          $scope.items.map(function(item) {
            if (item.id === id) {
              checkItems.push(item);
            }
          })
        }

        if (id == undefined && auditItems.length == 0) {
           Toastr.error("请至少选择一个订单");
           return;
        }

        // 验证申请人和审核人是否相同
        if ($scope.checkAuditor(checkItems)) {
          Toastr.error('不能审核自己提交的退款单');
          return;
        }

        $modal.open({
          templateUrl: '/views/afterSales/dialog/auditDialog.html',
          controller: 'AuditDialogCtrl',
          backdrop: 'static',
          size: 'sm',
          resolve: {
            data: function () {
              return {
                type: type,
                orderId: orderId
              };
            }
          }
        }).result.then(function(res) {
          if(auditItems.length == 0 && orderId != undefined) {
            if (item.reason === '回馈金包退款') {
              res.id = id;
              res.role = $scope.role;
              res.bonusPendVOS = item.bonusPendVOS;
              res.userId = item.userId;
              res.orderId = item.orderId;
              Retund.audit(res).then(function(){
                  $scope.getList($scope.pagination.page)
              });  
            } else {
              res.id = id;
              res.role = $scope.role;
              Retund.audit(res).then(function(){
                  $scope.getList($scope.pagination.page)
              });
            }
          }

          if(auditItems.length > 0 && orderId == undefined) {
            res.idList = auditItems;
            res.role = $scope.role;
            res.confirms = confirms;
            Retund.batchAudit(res).then(function(){
                $scope.getList($scope.pagination.page)
            });
          }
        });
      };

      // 判断退款申请单提交人是否与审核人为同一账号
      $scope.checkAuditor = function(checkItems) {
        for(var i in checkItems) {
          if (checkItems[i].submitter === $rootScope.uid) {
            return true
          }
        }
        return false
      }
    }
  ]);