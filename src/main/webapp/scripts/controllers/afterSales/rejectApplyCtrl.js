
'use strict';
/**
 * @ngdoc controller
 * @name adminApp.controller:rejectApply
 * @description
 * @require
 * <AUTHOR>
 * @date Fri Jul 13 2018 10:41:05 GMT+0800 (CST)
 */
angular.module('adminApp')
  .controller('rejectApplyCtrl', function ($scope, rejectApply, $route, INIT_PAGINATION, afterSalesService, createModalFactory, kfCommon, toastr, userService, toolService, returnService, validator) {
    'ngInject';
    var vm = this;

    var applyid = $route.current.params.applyid || '';

    $scope.pagination = INIT_PAGINATION;

    $scope.result = [];

    var rangeDate = Tool.initRangeDate();

    $scope.search = {
      updateStartTime: rangeDate.startTime,
      updateEndTime: rangeDate.endTime,
      orderId: '',
      itemId: '',
      applyId: applyid,
      skuId: '',
      trackingNum: '',
      // TODO 提测前改为 -1
      // status: -1,
      status: -1,
      mode: 1,
      userCrmType: -1,
      deliveryStoreHouseType: -1,
      intercept: -1,
      scene: -1
    };

    $scope.INTERCEPT_STATUSES = [
      [-1, '全部'],
      [1, 'OMS拦截'],
      [2, '系统工单'],
      [3, '人工'],
  ];

    validator.setRules({
      datetimepicker1: {
        required: '开始时间不能为空'
      },
      datetimepicker2: {
        required: '结束时间不能为空'
      }
    });

    init();

    $scope.query = function () {
      $scope.pagination.page = 1;
      search();
    };

    $scope.showReturnDelivery = function (expressCompany, trackingNum) {
      afterSalesService.showReturnDelivery({
        expressCompany: expressCompany,
        trackingNum: trackingNum
      });
    };

    $scope.showAfterSale = function (item) {
      afterSalesService.showAfterSale({
        item: item,
        type: 5,
        commAfterSaleTrac: true
      });
    };

    $scope.showKefuRemark = function (item) {
      item.id = item.applyId;
      afterSalesService.openRemarkDialog({
        id: item.applyId,
        item: item,
        remark: item.remark || { remark: '' },
        commPromise: false,
        type: 'asReject',
        callback: function (data) {
          returnService.returnRemark(data.submitParam).then(function (data) {
            search();
          });
        }
      });
    };

    $scope.export = function () {
      // 检查必填字段：开始时间和结束时间不可为空
      if (!$scope.search.updateStartTime) {
        toastr.error('开始时间不能为空');
        return;
      }
      if (!$scope.search.updateEndTime) {
        toastr.error('结束时间不能为空');
        return;
      }

      var params = getParams();
      window.open('/yxkf/rejectApply/export?' + $.param(params));
    };

    $scope.detail = function (item) {
      createModalFactory({
        templateUrl: '/views/afterSales/dialog/rejectApplyDetail.html',
        ctrl: 'rejectApplyDetailCtrl',
        size: 'big',
        data: {
          item: item
        },
        success: function () {
          search();
        }
      });
    };

    // 信誉标签点击事件
    $scope.openLink = function (label, userName, userId) {
      if (!userName) {
        return;
      }
      userService.queryUserOrderInfo({ userName: userName, userId: userId }).then(function (res) {
        toolService.openLink(label, res.userid)
      })
    }

    // 信誉标签hover展示下划线
    $scope.isCreditLink = function (label) {
      return /^R[1|2|3|4|5]{1}$/.test(label);
    }

    $scope.change = search;

    function search() {
      var params = getParams();

      rejectApply.list(params).then(function (data) {
        $scope.result = data.result;
        $scope.pagination = data.pagination;
      });
    }

    function getParams() {
      var params = $.extend({}, $scope.search, { page: $scope.pagination.page, size: $scope.pagination.size });

      if ($scope.search.mode == 1) {
        params.itemId = $scope.search.otherId;
      } else {
        params.skuId = $scope.search.otherId;
      }

      params = Tool.setParamsDefaultVaule(params, ['userId', 'orderId', 'itemId', 'skuId']);

      if (params.orderId || params.userId || params.itemId || params.skuId || params.applyId) {
        params.status = -1;
      }

      delete params.otherId;
      delete params.mode;
      return params;
    }

    function init() {
      search();
    }

  });