'use strict';
/**
 * @ngdoc controller
 * @name adminApp.controller:return
 * @description
 * @require
 * <AUTHOR>
 * @date Wed Oct 11 2017 19:30:04 GMT+0800 (CST)
 */
angular.module('adminApp')
  .controller('channelReturn', ['$scope', 'channelReturn', 'createModalFactory', 'thirdparty', 'afterSalesService', 'toastr', 'validator',
      function ($scope, channelReturn, CreateModalFactory, Thirdparty, afterSalesService, Toastr, validator) {
          var vm = this;

          // 默认设置时间区间为现在到过去30天
          var date = new Date();
          var endTime = date.getTime();
          var startTime;

          date.setDate(date.getDate() - 30);
          startTime = date.getTime();

          // 快递签收状态
          $scope.receiptStatusList = [
              [-1, '全部'],
              [0, '待签收'],
              [1, '已签收']
          ];

          $scope.SEARCH_STATUSES = [
              [0, '全部'],
              [1, '待审核'],
              [2, '待用户寄回商品'],
              [3, '待仓库收货质检'],
              [4, '退货完成'],
              [5, '审核不通过'],
              [6, '用户取消'],
              [7, '系统取消'],
              [8, '客服取消'],
              [9, '待客服确认'],
              [11, '客服拒绝'],
              [16, '异常关闭'],
              [100, '审核通过，直接退款']
          ];

          // 初始化默认搜索值


          $scope.channelIds = [];

          $scope.timeGroup = {
              startTime: startTime,
              endTime: endTime
          }

          $scope.searchType = 1;
          $scope.searchKey = '';
          $scope.timeType = 1;

          $scope.result = [];
          $scope.channelList = [];

          $scope.pagination = {
              page: 1,
              size: 10,
              totalPage: 0,
              total: 0
          };

          $scope.search = {
              statusList: 0,
              receiptStatus: -1,
              type: 2,
              channelIds: "",
              page: $scope.pagination.page,
              size: $scope.pagination.size
          };
          $scope.searchTypeChange = function () {
              $scope.searchKey = '';
          }

          // 设置验证规则
          validator.setRules({
              datetimepicker1: {
                  required: '开始时间不能为空'
              },
              datetimepicker2: {
                  required: '结束时间不能为空'
              }
          });

          $scope.change = function () {
              if (($scope.timeGroup.endTime - $scope.timeGroup.startTime) / 86400000 > 180) {
                Toastr.error('选择时间区间不得超过6个月');
                return;
              }
              if ($scope.timeType == 2) {
                  $scope.search.updateStartTime = $scope.timeGroup.startTime;
                  $scope.search.updateEndTime = $scope.timeGroup.endTime;
                  $scope.search.createStartTime = undefined;
                  $scope.search.createEndTime = undefined;
              } else {
                  $scope.search.updateStartTime = undefined;
                  $scope.search.updateEndTime = undefined;
                  $scope.search.createStartTime = $scope.timeGroup.startTime;
                  $scope.search.createEndTime = $scope.timeGroup.endTime;
              }
              var page = {page: $scope.pagination.page, size: $scope.pagination.size};
              var params = $.extend({}, $scope.search, page);
              if (params.applyId) {
                  params.startTime = 0;
                  params.endTime = 0;
              }
              params = removeEmptyProp(params);
              if (params.channelIds == null) {
                  params.channelIds = "";
              }

              channelReturn.list(params).then(function (res) {
                  $scope.pagination = res.pagination;
                  $scope.result = res.result;
              });
          };

          $scope.doSearch = function () {
              if ($scope.channelIds.length === $scope.channelList.length || $scope.channelIds.length === 0) {
                  $scope.search.channelIds = "";
              } else {
                  var channelIds = $scope.channelIds.map(function (item) {
                      return item.value
                  })
                  $scope.search.channelIds = channelIds.join(',');
              }
              $scope.pagination.page = 1;
              if ($scope.searchType == 1) {
                  $scope.search.itemId = $scope.searchKey;
                  $scope.search.skuId = '';
              } else {
                  $scope.search.skuId = $scope.searchKey;
                  $scope.search.itemId = '';
              }
              $scope.change();
          };

          $scope.change();

          $scope.showReturnDelivery = function (expressCompany, trackingNum, applyId) {
              afterSalesService.showReturnDelivery({
                  expressCompany: expressCompany,
                  trackingNum: trackingNum
              });
          };

          // 查看售后轨迹
          $scope.showAfterSale = function (item) {
              afterSalesService.showAfterSale({
                  item: item,
                  channelType: 1
              });
          };

          $scope.aduit = function (item) {
              CreateModalFactory({
                  templateUrl: '/views/channel/dialog/returnDetail.html',
                  ctrl: 'channelReturnDetail',
                  ctrlAs: '',
                  data: {
                      item: item
                  },
                  success: function () {
                      $scope.change();
                  }
              });
          };

          $scope.remark = function (item) {
              var params = {applyId: item.applyId};
              channelReturn.queryRemark(params).then(function (remark) {
                  if (remark.length) {
                      remark = {
                          remark: remark[0].remark,
                          submitTime: remark[0].modifyTime,
                          submitter: remark[0].submitter
                      };
                  } else {
                      remark = {};
                  }
                  openRemarkDialog(item, remark);
              });
          };

          $scope.export = function () {
              // 检查必填字段：开始时间和结束时间不可为空
              if (!$scope.timeGroup.startTime) {
                  Toastr.error('开始时间不能为空');
                  return;
              }
              if (!$scope.timeGroup.endTime) {
                  Toastr.error('结束时间不能为空');
                  return;
              }

              var params = $.param($scope.search);
              window.open('/yxkf/return/channel/export?' + params)
          }

          Thirdparty.getAllChannelV2().then(function (data) {
              data = data || [];
              var channelList = data.map(function (item) {
                  return {
                      value: item.channelId,
                      name: item.channelDesc
                  }
              });
              // $scope.channelList = [{value: -1, name: '全部'}].concat(channelList);
              $scope.channelList = channelList;
          });

          // 打开备注弹框
          function openRemarkDialog(item, remark) {
            //   console.log('openRemarkDialog');
              CreateModalFactory({
                  templateUrl: '/views/channel/dialog/returnRemark.html',
                  ctrl: 'ChannelRemarkCtrl',
                  data: {
                      item: item,
                      params: {
                          type: 'exchange',
                          remark: remark
                      }
                  },
                  success: function (data) {
                      var params = {
                          applyId: item.applyId,
                          remark: data.remark
                      };
                      channelReturn.remark(params).then(function () {
                          Toastr.success('操作成功');
                          $scope.change();
                      });
                  }
              });
          }

          function removeEmptyProp(obj) {
              var newObj = {};
              Object.keys(obj).map(function (p) {
                  if (obj[p] !== '') {
                      newObj[p] = obj[p];
                  }
              });
              return newObj;
          }

      }]);
